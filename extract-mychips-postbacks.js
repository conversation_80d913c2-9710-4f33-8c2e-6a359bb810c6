const fs = require('fs');

function extractMychipsPostbacks() {
  try {
    console.log('📖 Reading mychipsresults.json...');
    const data = JSON.parse(fs.readFileSync('mychipsresults.json', 'utf8'));
    
    console.log(`📊 Analyzing ${data.length} MyChips offers for postbacks...`);
    
    const postbackData = [];
    
    data.forEach(offer => {
      const offerId = offer.id;
      const title = offer.title;
      const status = offer.status;
      const redirectChain = offer.redirectChain || [];
      
      if (status === 'failed' || redirectChain.length === 0) {
        return; // Skip failed offers or those without redirects
      }
      
      // Look for postback URLs in the redirect chain
      const postbacks = redirectChain.filter(redirect => {
        const url = redirect.url.toLowerCase();
        return url.includes('postback') || 
               url.includes('callback') || 
               url.includes('pixel') ||
               url.includes('conversion') ||
               url.includes('track') ||
               url.includes('event') ||
               url.includes('complete') ||
               url.includes('success');
      });
      
      // Also look for URLs with common postback parameters
      const parameterBasedPostbacks = redirectChain.filter(redirect => {
        const url = redirect.url;
        return url.includes('click_id=') ||
               url.includes('transaction_id=') ||
               url.includes('conversion_id=') ||
               url.includes('sub_id=') ||
               url.includes('aff_sub=') ||
               url.includes('s1=') ||
               url.includes('s2=') ||
               url.includes('payout=') ||
               url.includes('revenue=') ||
               url.includes('cid=') ||
               url.includes('user_id=') ||
               url.includes('adunit_id=');
      });
      
      // Combine both types
      const allPostbacks = [...new Set([...postbacks, ...parameterBasedPostbacks])];
      
      if (allPostbacks.length > 0) {
        // Extract relevant cookies that might be needed
        const relevantCookies = [];
        redirectChain.forEach(redirect => {
          if (redirect.cookies && redirect.cookies.length > 0) {
            redirect.cookies.forEach(cookie => {
              if (cookie.name.includes('click') ||
                  cookie.name.includes('track') ||
                  cookie.name.includes('session') ||
                  cookie.name.includes('id') ||
                  cookie.name.includes('aff') ||
                  cookie.name.includes('sub') ||
                  cookie.name.includes('NID') ||
                  cookie.name.includes('SID')) {
                relevantCookies.push({
                  name: cookie.name,
                  domain: cookie.domain,
                  value: cookie.value ? cookie.value.substring(0, 50) + '...' : 'N/A'
                });
              }
            });
          }
        });
        
        // Extract URL parameters that might be needed
        const relevantParams = new Set();
        allPostbacks.forEach(postback => {
          try {
            const url = new URL(postback.url);
            url.searchParams.forEach((value, key) => {
              if (key.includes('click') ||
                  key.includes('track') ||
                  key.includes('id') ||
                  key.includes('sub') ||
                  key.includes('aff') ||
                  key.includes('conversion') ||
                  key.includes('transaction') ||
                  key.includes('payout') ||
                  key.includes('revenue') ||
                  key.includes('cid') ||
                  key.includes('user') ||
                  key.includes('adunit') ||
                  key.includes('preview') ||
                  key.includes('language') ||
                  key.includes('pid') ||
                  key.includes('rt')) {
                relevantParams.add(key);
              }
            });
          } catch (e) {
            // Skip invalid URLs
          }
        });
        
        postbackData.push({
          offerId: offerId,
          title: title,
          status: status,
          postbackUrls: allPostbacks.map(pb => pb.url),
          relevantCookies: relevantCookies,
          relevantParameters: Array.from(relevantParams),
          totalRedirects: redirectChain.length
        });
      }
    });
    
    // Sort by offer ID
    postbackData.sort((a, b) => {
      // Handle different ID formats
      const aId = typeof a.offerId === 'string' ? a.offerId : String(a.offerId);
      const bId = typeof b.offerId === 'string' ? b.offerId : String(b.offerId);
      return aId.localeCompare(bId);
    });
    
    // Save results
    fs.writeFileSync('mychips_postback_analysis.json', JSON.stringify(postbackData, null, 2));
    
    console.log(`✅ Found postbacks in ${postbackData.length} MyChips offers`);
    console.log(`💾 Saved analysis to: mychips_postback_analysis.json`);
    
    // Show summary
    console.log('\n📋 Summary:');
    postbackData.slice(0, 5).forEach(offer => {
      console.log(`\n🎯 Offer ${offer.offerId} (${offer.title}):`);
      console.log(`   Status: ${offer.status}`);
      console.log(`   Postbacks: ${offer.postbackUrls.length}`);
      offer.postbackUrls.slice(0, 2).forEach((url, index) => {
        try {
          const domain = new URL(url).hostname;
          console.log(`   ${index + 1}. ${domain}`);
        } catch (e) {
          console.log(`   ${index + 1}. ${url.substring(0, 50)}...`);
        }
      });
      if (offer.relevantParameters.length > 0) {
        console.log(`   Key params: ${offer.relevantParameters.slice(0, 5).join(', ')}`);
      }
      if (offer.relevantCookies.length > 0) {
        console.log(`   Key cookies: ${offer.relevantCookies.slice(0, 3).map(c => c.name).join(', ')}`);
      }
    });
    
    if (postbackData.length > 5) {
      console.log(`\n... and ${postbackData.length - 5} more offers`);
    }
    
  } catch (error) {
    console.error('❌ Error extracting MyChips postbacks:', error.message);
  }
}

// Run the extraction
console.log('🔍 Starting MyChips postback extraction...\n');
extractMychipsPostbacks();
console.log('\n✨ Extraction completed!');
