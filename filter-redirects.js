const fs = require('fs');

function filterRedirectResults() {
  try {
    console.log('📖 Reading redirect_results.json...');
    const data = JSON.parse(fs.readFileSync('redirect_results.json', 'utf8'));
    
    console.log(`📊 Total offers to analyze: ${data.length}`);
    
    // Filter offers that have more than just the kloverapp redirect
    const offersWithMultipleRedirects = data.filter(offer => {
      const redirectChain = offer.redirectChain || [];
      
      // Count non-kloverapp redirects
      const nonKloverRedirects = redirectChain.filter(redirect => 
        !redirect.url.includes('kloverapp.go2cloud.org')
      );
      
      // Keep offers that have redirects beyond just kloverapp
      return nonKloverRedirects.length > 0;
    });
    
    console.log(`✅ Offers with additional redirects: ${offersWithMultipleRedirects.length}`);
    console.log(`❌ Offers with only kloverapp: ${data.length - offersWithMultipleRedirects.length}`);
    
    // Show some examples of what we're keeping
    console.log('\n🔍 Sample of offers being kept:');
    offersWithMultipleRedirects.slice(0, 3).forEach(offer => {
      console.log(`  Offer ${offer.offerId}: ${offer.redirectChain.length} redirects`);
      offer.redirectChain.forEach((redirect, index) => {
        const domain = new URL(redirect.url).hostname;
        console.log(`    ${index + 1}. ${domain}`);
      });
      console.log('');
    });
    
    // Save filtered results
    fs.writeFileSync('redirect_results_filtered.json', JSON.stringify(offersWithMultipleRedirects, null, 2));
    
    console.log(`💾 Saved filtered results to: redirect_results_filtered.json`);
    console.log(`📉 Reduction: ${data.length} → ${offersWithMultipleRedirects.length} offers (${((1 - offersWithMultipleRedirects.length / data.length) * 100).toFixed(1)}% removed)`);
    
  } catch (error) {
    console.error('❌ Error filtering redirect results:', error.message);
  }
}

// Run the filter
console.log('🔄 Starting redirect filtering process...\n');
filterRedirectResults();
console.log('\n✨ Filtering completed!');
