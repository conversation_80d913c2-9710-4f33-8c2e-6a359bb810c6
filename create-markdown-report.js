const fs = require('fs');

function createMarkdownReport() {
  try {
    console.log('📖 Reading redirect_results.json...');
    const data = JSON.parse(fs.readFileSync('redirect_results.json', 'utf8'));
    
    console.log(`📊 Processing ${data.length} offers for markdown report...`);
    
    let markdown = `# Redirect Results Report\n\n`;
    markdown += `**Generated:** ${new Date().toLocaleString()}\n`;
    markdown += `**Total Offers:** ${data.length}\n\n`;
    
    // Group offers by patterns for better organization
    const offerGroups = new Map();
    
    data.forEach(offer => {
      const firstUrl = offer.redirectChain?.[0]?.url || '';
      let pattern = 'No Redirects';
      
      if (firstUrl.includes('kloverapp.go2cloud.org')) {
        // Check if it has additional redirects beyond kloverapp
        const nonKloverRedirects = offer.redirectChain.filter(r => 
          !r.url.includes('kloverapp.go2cloud.org')
        );
        
        if (nonKloverRedirects.length === 0) {
          pattern = 'Kloverapp Only';
        } else {
          // Get the main destination domain
          try {
            const mainDomain = new URL(nonKloverRedirects[0].url).hostname;
            pattern = `Kloverapp → ${mainDomain}`;
          } catch (e) {
            pattern = 'Kloverapp → Other';
          }
        }
      } else if (firstUrl) {
        try {
          pattern = new URL(firstUrl).hostname;
        } catch (e) {
          pattern = 'Other';
        }
      }
      
      if (!offerGroups.has(pattern)) {
        offerGroups.set(pattern, []);
      }
      offerGroups.get(pattern).push(offer);
    });
    
    // Sort groups by size (largest first)
    const sortedGroups = Array.from(offerGroups.entries())
      .sort(([,a], [,b]) => b.length - a.length);
    
    // Add summary table
    markdown += `## Summary by Pattern\n\n`;
    markdown += `| Pattern | Count | Percentage |\n`;
    markdown += `|---------|-------|------------|\n`;
    
    sortedGroups.forEach(([pattern, offers]) => {
      const percentage = ((offers.length / data.length) * 100).toFixed(1);
      markdown += `| ${pattern} | ${offers.length} | ${percentage}% |\n`;
    });
    
    markdown += `\n---\n\n`;
    
    // Add detailed sections for each pattern
    sortedGroups.forEach(([pattern, offers]) => {
      markdown += `## ${pattern} (${offers.length} offers)\n\n`;
      
      // Show first few examples
      const samplesToShow = Math.min(5, offers.length);
      
      for (let i = 0; i < samplesToShow; i++) {
        const offer = offers[i];
        markdown += `### Offer ${offer.offerId}\n\n`;
        markdown += `- **User Agent:** ${offer.userAgent.substring(0, 80)}...\n`;
        markdown += `- **IP:** ${offer.ip}\n`;
        markdown += `- **Status:** ${offer.status}\n`;
        markdown += `- **Redirect Chain Length:** ${offer.redirectChain?.length || 0}\n\n`;
        
        if (offer.redirectChain && offer.redirectChain.length > 0) {
          markdown += `**Redirect Chain:**\n\n`;
          offer.redirectChain.slice(0, 10).forEach((redirect, index) => {
            try {
              const url = new URL(redirect.url);
              const domain = url.hostname;
              const path = url.pathname.length > 50 ? 
                url.pathname.substring(0, 50) + '...' : url.pathname;
              
              markdown += `${index + 1}. **${domain}**${path}\n`;
              
              // Show key parameters if any
              const keyParams = [];
              url.searchParams.forEach((value, key) => {
                if (key.includes('id') || key.includes('aff') || key.includes('click') || 
                    key.includes('offer') || key.includes('sub')) {
                  keyParams.push(`${key}=${value.substring(0, 20)}${value.length > 20 ? '...' : ''}`);
                }
              });
              
              if (keyParams.length > 0) {
                markdown += `   - Params: ${keyParams.slice(0, 3).join(', ')}\n`;
              }
              
              // Show cookies if any
              if (redirect.cookies && redirect.cookies.length > 0) {
                const cookieNames = redirect.cookies.slice(0, 3).map(c => c.name);
                markdown += `   - Cookies: ${cookieNames.join(', ')}\n`;
              }
              
            } catch (e) {
              markdown += `${index + 1}. ${redirect.url.substring(0, 100)}...\n`;
            }
          });
          
          if (offer.redirectChain.length > 10) {
            markdown += `... and ${offer.redirectChain.length - 10} more redirects\n`;
          }
        }
        
        markdown += `\n---\n\n`;
      }
      
      if (offers.length > samplesToShow) {
        markdown += `*... and ${offers.length - samplesToShow} more offers in this pattern*\n\n`;
      }
    });
    
    // Add appendix with statistics
    markdown += `## Statistics\n\n`;
    
    // User agent analysis
    const userAgents = new Map();
    data.forEach(offer => {
      const ua = offer.userAgent;
      if (ua) {
        // Extract browser info
        const browserMatch = ua.match(/(Chrome|Firefox|Safari|Edge)\/[\d.]+/);
        const browser = browserMatch ? browserMatch[0] : 'Unknown';
        userAgents.set(browser, (userAgents.get(browser) || 0) + 1);
      }
    });
    
    markdown += `### Top User Agents\n\n`;
    Array.from(userAgents.entries())
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10)
      .forEach(([ua, count]) => {
        markdown += `- **${ua}:** ${count} requests\n`;
      });
    
    markdown += `\n### Status Distribution\n\n`;
    const statusCounts = new Map();
    data.forEach(offer => {
      statusCounts.set(offer.status, (statusCounts.get(offer.status) || 0) + 1);
    });
    
    Array.from(statusCounts.entries()).forEach(([status, count]) => {
      const percentage = ((count / data.length) * 100).toFixed(1);
      markdown += `- **${status}:** ${count} (${percentage}%)\n`;
    });
    
    // Save the markdown file
    fs.writeFileSync('redirect_results_report.md', markdown);
    
    console.log(`✅ Created markdown report with ${sortedGroups.length} pattern groups`);
    console.log(`💾 Saved to: redirect_results_report.md`);
    console.log(`📄 Report contains ${markdown.split('\n').length} lines`);
    
  } catch (error) {
    console.error('❌ Error creating markdown report:', error.message);
  }
}

// Run the report creation
console.log('📝 Creating markdown report from redirect_results.json...\n');
createMarkdownReport();
console.log('\n✨ Markdown report completed!');
