const fs = require('fs');
const { chromium } = require('playwright');
const fetch = require('node-fetch');

// Static aff_unique1 value you gave
const STATIC_UNIQUE_1 = '21fa24a2420ca89e6884c4bcd3e0c166e680231f';

// Offer link generator
const OFFER_URL_TEMPLATE = (id) =>
  `https://kloverapp.go2cloud.org/aff_c?offer_id=${id}&aff_id=3&aff_unique1=${STATIC_UNIQUE_1}`;

// LSR template with dynamic offer_id
const LSR_URL_TEMPLATE = (offerId, transactionId) =>
  `https://kloverapp.go2cloud.org/aff_lsr?offer_id=${offerId}&transaction_id=${transactionId}`;

const OUTPUT_PATH = 'redirect_results.json';
const offerIds = Array.from({ length: 2000 }, (_, i) => i + 1);

// User Agents
const androidUAs = [
  'Mozilla/5.0 (Linux; Android 13; Pixel 7 Pro) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114 Mobile Safari/537.36',
  'Mozilla/5.0 (Linux; Android 12; SM-G991U) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/113 Mobile Safari/537.36',
];
const iosUAs = [
  'Mozilla/5.0 (iPhone; CPU iPhone OS 16_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.0 Mobile Safari/604.1',
  'Mozilla/5.0 (iPhone; CPU iPhone OS 15_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.6 Mobile Safari/604.1',
];

function getRandomFrom(arr) {
  return arr[Math.floor(Math.random() * arr.length)];
}

function getRandomIP() {
  return `${randomInt(34, 55)}.${randomInt(0, 255)}.${randomInt(0, 255)}.${randomInt(0, 255)}`;
}

function randomInt(min, max) {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

function extractTransactionId(chain) {
  for (const entry of chain) {
    const match = entry.url.match(/transaction_id=([\w\d]+)/);
    if (match) return match[1];
  }
  return null;
}

async function visitWithUA(browser, offerId, userAgent, ip) {
  const context = await browser.newContext({
    userAgent,
    extraHTTPHeaders: {
      'X-Forwarded-For': ip,
      'Client-IP': ip,
      'X-Network-Type': '4g',
    },
    viewport: { width: 390, height: 844 },
    isMobile: true,
    deviceScaleFactor: 2,
  });

  const page = await context.newPage();
  const result = {
    offerId,
    userAgent,
    ip,
    redirectChain: [],
    status: 'unknown',
  };

  const visited = new Set();

  try {
    page.on('response', async (response) => {
      const url = response.url();
      if (!visited.has(url)) {
        visited.add(url);
        const cookies = await context.cookies(url);
        result.redirectChain.push({ url, cookies });
      }
    });

    const offerUrl = OFFER_URL_TEMPLATE(offerId);
    await page.goto(offerUrl, { waitUntil: 'load', timeout: 15000 });
    result.status = 'success';

    const transactionId = extractTransactionId(result.redirectChain);
    if (transactionId) {
      const lsrUrl = LSR_URL_TEMPLATE(offerId, transactionId);
      const lsrRes = await fetch(lsrUrl, {
        headers: {
          'User-Agent': userAgent,
          'X-Forwarded-For': ip,
          'Client-IP': ip,
        },
      });

      result.lsrFired = lsrRes.ok;
      result.lsrStatus = lsrRes.status;
    }
  } catch (err) {
    result.status = 'failed';
    result.error = err.message;
  } finally {
    await page.close();
    await context.close();
  }

  return result;
}

// Main runner
(async () => {
  const browser = await chromium.launch({ headless: true });
  const results = [];

  for (const offerId of offerIds) {
    const userAgent = Math.random() < 0.5 ? getRandomFrom(androidUAs) : getRandomFrom(iosUAs);
    const ip = getRandomIP();

    console.log(`🔍 Offer ${offerId} | ${userAgent.includes('Android') ? 'Android' : 'iOS'} | IP ${ip}`);
    const result = await visitWithUA(browser, offerId, userAgent, ip);

    console.log(JSON.stringify(result, null, 2));
    results.push(result);

    await new Promise((res) => setTimeout(res, 2500));
  }

  await browser.close();
  fs.writeFileSync(OUTPUT_PATH, JSON.stringify(results, null, 2));
  console.log(`✅ Results saved to ${OUTPUT_PATH}`);
})();
