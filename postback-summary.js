const fs = require('fs');

function createPostbackSummary() {
  try {
    console.log('📖 Reading postback analysis...');
    const data = JSON.parse(fs.readFileSync('postback_analysis.json', 'utf8'));
    
    const summary = [];
    
    data.forEach(offer => {
      const offerId = offer.offerId;
      
      // Find the main postback URL (usually the one with tracking parameters)
      let mainPostback = null;
      
      // Look for URLs that are clearly postbacks (not tracking pixels)
      const candidatePostbacks = offer.postbackUrls.filter(url => {
        const urlLower = url.toLowerCase();
        return !urlLower.includes('pixel') &&
               !urlLower.includes('facebook') &&
               !urlLower.includes('google') &&
               !urlLower.includes('doubleclick') &&
               !urlLower.includes('criteo') &&
               !urlLower.includes('quantserve') &&
               !urlLower.includes('.js') &&
               (urlLower.includes('postback') ||
                urlLower.includes('callback') ||
                urlLower.includes('conversion') ||
                url.includes('click_id=') ||
                url.includes('transaction_id=') ||
                url.includes('aff_sub') ||
                url.includes('s1=') ||
                url.includes('s2='));
      });
      
      // If no clear postback, look for the main destination URL with parameters
      if (candidatePostbacks.length === 0) {
        const destinationUrls = offer.postbackUrls.filter(url => {
          try {
            const urlObj = new URL(url);
            return urlObj.searchParams.has('a') || 
                   urlObj.searchParams.has('aff_id') ||
                   urlObj.searchParams.has('offer_id') ||
                   urlObj.searchParams.has('click_id');
          } catch (e) {
            return false;
          }
        });
        mainPostback = destinationUrls[0];
      } else {
        mainPostback = candidatePostbacks[0];
      }
      
      if (mainPostback) {
        try {
          const url = new URL(mainPostback);
          const domain = url.hostname;
          
          // Extract key parameters
          const keyParams = {};
          const importantParamNames = [
            'offer_id', 'aff_id', 'click_id', 'transaction_id', 'conversion_id',
            'aff_sub', 'aff_unique1', 'aff_click_id', 's1', 's2', 's3', 's4', 's5',
            'sub_id', 'payout', 'revenue', 'a', 'c', 'rid', 'cid'
          ];
          
          importantParamNames.forEach(param => {
            if (url.searchParams.has(param)) {
              keyParams[param] = url.searchParams.get(param);
            }
          });
          
          // Extract relevant cookies
          const keyCookies = offer.relevantCookies.filter(cookie => {
            const name = cookie.name.toLowerCase();
            return name.includes('session') ||
                   name.includes('click') ||
                   name.includes('aff') ||
                   name.includes('track') ||
                   name.includes('id');
          }).slice(0, 3); // Limit to top 3 most relevant
          
          summary.push({
            offerId: offerId,
            postbackUrl: mainPostback,
            domain: domain,
            keyParameters: keyParams,
            requiredCookies: keyCookies.map(c => ({
              name: c.name,
              domain: c.domain
            })),
            totalPostbacks: offer.postbackUrls.length
          });
        } catch (e) {
          console.log(`⚠️  Could not parse URL for offer ${offerId}`);
        }
      }
    });
    
    // Sort by offer ID
    summary.sort((a, b) => a.offerId - b.offerId);
    
    // Save summary
    fs.writeFileSync('postback_summary.json', JSON.stringify(summary, null, 2));
    
    console.log(`✅ Created summary for ${summary.length} offers with postbacks`);
    console.log(`💾 Saved to: postback_summary.json`);
    
    // Show first few examples
    console.log('\n📋 Sample postbacks:');
    summary.slice(0, 5).forEach(offer => {
      console.log(`\n🎯 Offer ${offer.offerId} (${offer.domain}):`);
      console.log(`   URL: ${offer.postbackUrl.substring(0, 100)}...`);
      if (Object.keys(offer.keyParameters).length > 0) {
        console.log(`   Key params: ${Object.keys(offer.keyParameters).join(', ')}`);
      }
      if (offer.requiredCookies.length > 0) {
        console.log(`   Required cookies: ${offer.requiredCookies.map(c => c.name).join(', ')}`);
      }
    });
    
    if (summary.length > 5) {
      console.log(`\n... and ${summary.length - 5} more offers`);
    }
    
  } catch (error) {
    console.error('❌ Error creating summary:', error.message);
  }
}

// Run the summary creation
console.log('📝 Creating postback summary...\n');
createPostbackSummary();
console.log('\n✨ Summary completed!');
