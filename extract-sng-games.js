const fs = require('fs');

function extractSngGames() {
  try {
    console.log('📖 Reading mychipsresults.json...');
    const data = JSON.parse(fs.readFileSync('mychipsresults.json', 'utf8'));
    
    console.log(`📊 Analyzing ${data.length} MyChips offers for SNG games...`);
    
    const sngGames = [];
    
    data.forEach(offer => {
      const title = offer.title || '';
      const redirectChain = offer.redirectChain || [];
      
      // Check if this is an SNG game by looking for SNG indicators
      const isSngGame = 
        title.toLowerCase().includes('sng') ||
        title.toLowerCase().includes('singular') ||
        redirectChain.some(redirect => 
          redirect.url.includes('sng.link') ||
          redirect.url.includes('singular') ||
          redirect.url.includes('sng')
        );
      
      if (isSngGame && offer.status === 'success') {
        // Try to extract payout information from redirect URLs
        let payout = 0;
        let payoutSource = 'unknown';
        
        redirectChain.forEach(redirect => {
          try {
            const url = new URL(redirect.url);
            
            // Look for common payout parameters
            const payoutParams = ['payout', 'revenue', 'amount', 'value', 'reward'];
            payoutParams.forEach(param => {
              if (url.searchParams.has(param)) {
                const value = parseFloat(url.searchParams.get(param));
                if (!isNaN(value) && value > payout) {
                  payout = value;
                  payoutSource = `${param} parameter`;
                }
              }
            });
            
            // Look for payout in URL path or query
            const urlString = redirect.url;
            const payoutMatches = urlString.match(/(?:payout|revenue|amount|value|reward)[=:](\d+\.?\d*)/i);
            if (payoutMatches) {
              const value = parseFloat(payoutMatches[1]);
              if (!isNaN(value) && value > payout) {
                payout = value;
                payoutSource = 'URL pattern match';
              }
            }
          } catch (e) {
            // Skip invalid URLs
          }
        });
        
        // If no payout found, try to extract from title or estimate based on game type
        if (payout === 0) {
          // Look for dollar amounts in title
          const titlePayoutMatch = title.match(/\$(\d+\.?\d*)/);
          if (titlePayoutMatch) {
            payout = parseFloat(titlePayoutMatch[1]);
            payoutSource = 'title';
          } else {
            // Default estimation based on game complexity/type
            if (title.toLowerCase().includes('casino') || title.toLowerCase().includes('slots')) {
              payout = 2.0; // Higher payout for casino games
              payoutSource = 'estimated (casino)';
            } else if (title.toLowerCase().includes('puzzle') || title.toLowerCase().includes('match')) {
              payout = 1.5; // Medium payout for puzzle games
              payoutSource = 'estimated (puzzle)';
            } else {
              payout = 1.0; // Default payout
              payoutSource = 'estimated (default)';
            }
          }
        }
        
        // Extract key redirect information
        const sngRedirects = redirectChain.filter(redirect => 
          redirect.url.includes('sng.link') || 
          redirect.url.includes('singular') ||
          redirect.url.includes('play.sng')
        );
        
        const mainSngUrl = sngRedirects.length > 0 ? sngRedirects[0].url : null;
        
        // Extract game ID if possible
        let gameId = offer.id;
        if (mainSngUrl) {
          try {
            const url = new URL(mainSngUrl);
            const idParam = url.searchParams.get('id') || url.searchParams.get('game_id') || url.searchParams.get('app_id');
            if (idParam) {
              gameId = idParam;
            }
          } catch (e) {
            // Keep original ID
          }
        }
        
        sngGames.push({
          id: offer.id,
          gameId: gameId,
          title: title,
          payout: payout,
          payoutSource: payoutSource,
          status: offer.status,
          attemptedWith: offer.attemptedWith,
          mainSngUrl: mainSngUrl,
          totalRedirects: redirectChain.length,
          sngRedirectCount: sngRedirects.length,
          redirectChain: redirectChain // Keep full chain for reference
        });
      }
    });
    
    // Sort by payout (highest first)
    sngGames.sort((a, b) => b.payout - a.payout);
    
    // Save the filtered and sorted results
    fs.writeFileSync('sng_games_sorted.json', JSON.stringify(sngGames, null, 2));
    
    console.log(`✅ Found ${sngGames.length} SNG games`);
    console.log(`💾 Saved sorted SNG games to: sng_games_sorted.json`);
    
    // Show top games
    console.log('\n🏆 Top 10 Highest Paying SNG Games:');
    sngGames.slice(0, 10).forEach((game, index) => {
      console.log(`${index + 1}. ${game.title}`);
      console.log(`   💰 Payout: $${game.payout.toFixed(2)} (${game.payoutSource})`);
      console.log(`   🎮 Game ID: ${game.gameId}`);
      console.log(`   📱 Platform: ${game.attemptedWith}`);
      if (game.mainSngUrl) {
        try {
          const domain = new URL(game.mainSngUrl).hostname;
          console.log(`   🔗 SNG Domain: ${domain}`);
        } catch (e) {
          console.log(`   🔗 SNG URL: ${game.mainSngUrl.substring(0, 50)}...`);
        }
      }
      console.log('');
    });
    
    // Show payout distribution
    console.log('📊 Payout Distribution:');
    const payoutRanges = {
      '$0.00-$0.99': 0,
      '$1.00-$1.99': 0,
      '$2.00-$4.99': 0,
      '$5.00-$9.99': 0,
      '$10.00+': 0
    };
    
    sngGames.forEach(game => {
      if (game.payout < 1) payoutRanges['$0.00-$0.99']++;
      else if (game.payout < 2) payoutRanges['$1.00-$1.99']++;
      else if (game.payout < 5) payoutRanges['$2.00-$4.99']++;
      else if (game.payout < 10) payoutRanges['$5.00-$9.99']++;
      else payoutRanges['$10.00+']++;
    });
    
    Object.entries(payoutRanges).forEach(([range, count]) => {
      const percentage = ((count / sngGames.length) * 100).toFixed(1);
      console.log(`   ${range}: ${count} games (${percentage}%)`);
    });
    
  } catch (error) {
    console.error('❌ Error extracting SNG games:', error.message);
  }
}

// Run the extraction
console.log('🎮 Starting SNG games extraction and sorting...\n');
extractSngGames();
console.log('\n✨ SNG extraction completed!');
