[{"offerId": "2586631", "title": "Forge of Empires", "status": "success", "postbackUrl": "https://api.mychips.io/v1.5/redirect/?cid=2586631&rt=2&pid=2469&user_id=f302f488-5f1c-43e6-91cb-4e82f6e21658&adunit_id=0ed871d3-c0e2-4f80-995a-4819039c496c&language=en", "domain": "api.mychips.io", "keyParameters": {"cid": "2586631", "rt": "2", "pid": "2469", "user_id": "f302f488-5f1c-43e6-91cb-4e82f6e21658", "adunit_id": "0ed871d3-c0e2-4f80-995a-4819039c496c", "language": "en"}, "requiredCookies": [{"name": "portal_tid", "domain": ".forgeofempires.com"}, {"name": "portal_tid", "domain": ".forgeofempires.com"}], "totalPostbacks": 2, "allPostbackDomains": ["api.mychips.io", "trk301.com"]}, {"offerId": "2596014", "title": "Acorns: Save & Invest", "status": "success", "postbackUrl": "https://api.mychips.io/v1.5/redirect/?cid=2596014&rt=2&pid=2469&user_id=f302f488-5f1c-43e6-91cb-4e82f6e21658&adunit_id=0ed871d3-c0e2-4f80-995a-4819039c496c&language=en", "domain": "api.mychips.io", "keyParameters": {"cid": "2596014", "rt": "2", "pid": "2469", "user_id": "f302f488-5f1c-43e6-91cb-4e82f6e21658", "adunit_id": "0ed871d3-c0e2-4f80-995a-4819039c496c", "language": "en"}, "requiredCookies": [{"name": "_cfuvid", "domain": ".vimeo.com"}, {"name": "_cfuvid", "domain": ".vimeo.com"}, {"name": "_cfuvid", "domain": ".highcharts.com"}], "totalPostbacks": 11, "allPostbackDomains": ["d.impactradius-event.com", "bat.bing.com", "api.mychips.io", "trk301.com", "www.acorns.com", "www.google-analytics.com", "analytics.google.com", "stats.g.doubleclick.net"]}, {"offerId": "2606816", "title": "PROMO-CODE! Scrambly: App Discovery & Cash Rewards", "status": "success", "postbackUrl": "https://api.mychips.io/v1.5/redirect/?cid=2606816&rt=2&pid=2469&user_id=f302f488-5f1c-43e6-91cb-4e82f6e21658&adunit_id=0ed871d3-c0e2-4f80-995a-4819039c496c&preview=1&language=en", "domain": "api.mychips.io", "keyParameters": {"cid": "2606816", "rt": "2", "pid": "2469", "user_id": "f302f488-5f1c-43e6-91cb-4e82f6e21658", "adunit_id": "0ed871d3-c0e2-4f80-995a-4819039c496c", "preview": "1", "language": "en"}, "requiredCookies": [{"name": "NID", "domain": ".google.com"}, {"name": "NID", "domain": ".google.com"}, {"name": "_gid", "domain": ".play.google.com"}], "totalPostbacks": 3, "allPostbackDomains": ["api.mychips.io", "trk301.com", "www.google-analytics.com"]}, {"offerId": "2607485", "title": "MONEY CASH - Play Games &amp; Earn", "status": "success", "postbackUrl": "https://api.mychips.io/v1.5/redirect/?cid=2607485&rt=2&pid=2469&user_id=f302f488-5f1c-43e6-91cb-4e82f6e21658&adunit_id=0ed871d3-c0e2-4f80-995a-4819039c496c&language=en", "domain": "api.mychips.io", "keyParameters": {"cid": "2607485", "rt": "2", "pid": "2469", "user_id": "f302f488-5f1c-43e6-91cb-4e82f6e21658", "adunit_id": "0ed871d3-c0e2-4f80-995a-4819039c496c", "language": "en"}, "requiredCookies": [{"name": "NID", "domain": ".google.com"}, {"name": "NID", "domain": ".google.com"}, {"name": "NID", "domain": ".google.com"}], "totalPostbacks": 5, "allPostbackDomains": ["api.mychips.io", "trk301.com", "play.sng.link", "www.google-analytics.com"]}, {"offerId": "2607505", "title": "Supreme King: <PERSON><PERSON><PERSON> Money Cash", "status": "success", "postbackUrl": "https://api.mychips.io/v1.5/redirect/?cid=2607505&rt=2&pid=2469&user_id=f302f488-5f1c-43e6-91cb-4e82f6e21658&adunit_id=0ed871d3-c0e2-4f80-995a-4819039c496c&language=en", "domain": "api.mychips.io", "keyParameters": {"cid": "2607505", "rt": "2", "pid": "2469", "user_id": "f302f488-5f1c-43e6-91cb-4e82f6e21658", "adunit_id": "0ed871d3-c0e2-4f80-995a-4819039c496c", "language": "en"}, "requiredCookies": [{"name": "NID", "domain": ".google.com"}, {"name": "NID", "domain": ".google.com"}, {"name": "NID", "domain": ".google.com"}], "totalPostbacks": 5, "allPostbackDomains": ["api.mychips.io", "trk301.com", "play.sng.link", "www.google-analytics.com"]}, {"offerId": "2607651", "title": "MPL <PERSON><PERSON> Skill : Win Real Cash", "status": "success", "postbackUrl": "https://api.mychips.io/v1.5/redirect/?cid=2607651&rt=2&pid=2469&user_id=f302f488-5f1c-43e6-91cb-4e82f6e21658&adunit_id=0ed871d3-c0e2-4f80-995a-4819039c496c&language=en", "domain": "api.mychips.io", "keyParameters": {"cid": "2607651", "rt": "2", "pid": "2469", "user_id": "f302f488-5f1c-43e6-91cb-4e82f6e21658", "adunit_id": "0ed871d3-c0e2-4f80-995a-4819039c496c", "language": "en"}, "requiredCookies": [], "totalPostbacks": 13, "allPostbackDomains": ["connect.facebook.net", "www.facebook.com", "www.google-analytics.com", "api.mychips.io", "trk301.com", "app.appsflyer.com", "www.mplgames.com", "analytics.google.com"]}, {"offerId": "2608300", "title": "<PERSON><PERSON><PERSON><PERSON>", "status": "success", "postbackUrl": "https://api.mychips.io/v1.5/redirect/?cid=2608300&rt=2&pid=2469&user_id=f302f488-5f1c-43e6-91cb-4e82f6e21658&adunit_id=0ed871d3-c0e2-4f80-995a-4819039c496c&language=en", "domain": "api.mychips.io", "keyParameters": {"cid": "2608300", "rt": "2", "pid": "2469", "user_id": "f302f488-5f1c-43e6-91cb-4e82f6e21658", "adunit_id": "0ed871d3-c0e2-4f80-995a-4819039c496c", "language": "en"}, "requiredCookies": [{"name": "__ps_did", "domain": ".kikoff.com"}, {"name": "ndp_session_id", "domain": ".kikoff.com"}, {"name": "tatari-session-cookie", "domain": ".kikoff.com"}], "totalPostbacks": 32, "allPostbackDomains": ["arttrk.com", "td.doubleclick.net", "googleads.g.doubleclick.net", "www.google.com", "s.axon.ai", "assets.apollo.io", "ads.nextdoor.com", "analytics.tiktok.com", "www.googleadservices.com", "d2hrivdxn8ekm8.cloudfront.net", "connect.facebook.net", "analytics.google.com", "flask.nextdoor.com", "t.co", "b.applovin.com", "analytics.twitter.com", "verifi.podscribe.com", "aplo-evnt.com", "wa.appsflyer.com", "capig.kikoff.com", "api.mychips.io", "trk301.com", "www.ojrq.net", "gtm.kikoff.com", "stats.g.doubleclick.net"]}, {"offerId": "2608819", "title": "Crypto.com", "status": "success", "postbackUrl": "https://api.mychips.io/v1.5/redirect/?cid=2608819&rt=2&pid=2469&user_id=f302f488-5f1c-43e6-91cb-4e82f6e21658&adunit_id=0ed871d3-c0e2-4f80-995a-4819039c496c&language=en", "domain": "api.mychips.io", "keyParameters": {"cid": "2608819", "rt": "2", "pid": "2469", "user_id": "f302f488-5f1c-43e6-91cb-4e82f6e21658", "adunit_id": "0ed871d3-c0e2-4f80-995a-4819039c496c", "language": "en"}, "requiredCookies": [], "totalPostbacks": 2, "allPostbackDomains": ["api.mychips.io", "trk301.com"]}, {"offerId": "2608824", "title": "<PERSON>", "status": "success", "postbackUrl": "https://api.mychips.io/v1.5/redirect/?cid=2608824&rt=2&pid=2469&user_id=f302f488-5f1c-43e6-91cb-4e82f6e21658&adunit_id=0ed871d3-c0e2-4f80-995a-4819039c496c&language=en", "domain": "api.mychips.io", "keyParameters": {"cid": "2608824", "rt": "2", "pid": "2469", "user_id": "f302f488-5f1c-43e6-91cb-4e82f6e21658", "adunit_id": "0ed871d3-c0e2-4f80-995a-4819039c496c", "language": "en"}, "requiredCookies": [], "totalPostbacks": 2, "allPostbackDomains": ["api.mychips.io", "trk301.com"]}, {"offerId": "2609793", "title": "Sweet Jam", "status": "success", "postbackUrl": "https://api.mychips.io/v1.5/redirect/?cid=2609793&rt=2&pid=2469&user_id=f302f488-5f1c-43e6-91cb-4e82f6e21658&adunit_id=0ed871d3-c0e2-4f80-995a-4819039c496c&language=en", "domain": "api.mychips.io", "keyParameters": {"cid": "2609793", "rt": "2", "pid": "2469", "user_id": "f302f488-5f1c-43e6-91cb-4e82f6e21658", "adunit_id": "0ed871d3-c0e2-4f80-995a-4819039c496c", "language": "en"}, "requiredCookies": [{"name": "NID", "domain": ".google.com"}, {"name": "NID", "domain": ".google.com"}, {"name": "NID", "domain": ".google.com"}], "totalPostbacks": 5, "allPostbackDomains": ["api.mychips.io", "trk301.com", "ciao.sng.link", "www.google-analytics.com"]}, {"offerId": "2609952", "title": "X2 Number Merge Puzzle 2048", "status": "success", "postbackUrl": "https://api.mychips.io/v1.5/redirect/?cid=2609952&rt=2&pid=2469&user_id=f302f488-5f1c-43e6-91cb-4e82f6e21658&adunit_id=0ed871d3-c0e2-4f80-995a-4819039c496c&language=en", "domain": "api.mychips.io", "keyParameters": {"cid": "2609952", "rt": "2", "pid": "2469", "user_id": "f302f488-5f1c-43e6-91cb-4e82f6e21658", "adunit_id": "0ed871d3-c0e2-4f80-995a-4819039c496c", "language": "en"}, "requiredCookies": [], "totalPostbacks": 2, "allPostbackDomains": ["api.mychips.io", "trk301.com"]}, {"offerId": "2610256", "title": "Block Smash ", "status": "success", "postbackUrl": "https://api.mychips.io/v1.5/redirect/?cid=2610256&rt=2&pid=2469&user_id=f302f488-5f1c-43e6-91cb-4e82f6e21658&adunit_id=0ed871d3-c0e2-4f80-995a-4819039c496c&language=en", "domain": "api.mychips.io", "keyParameters": {"cid": "2610256", "rt": "2", "pid": "2469", "user_id": "f302f488-5f1c-43e6-91cb-4e82f6e21658", "adunit_id": "0ed871d3-c0e2-4f80-995a-4819039c496c", "language": "en"}, "requiredCookies": [{"name": "NID", "domain": ".google.com"}, {"name": "NID", "domain": ".google.com"}, {"name": "_gid", "domain": ".play.google.com"}], "totalPostbacks": 5, "allPostbackDomains": ["api.mychips.io", "trk301.com", "ciao.sng.link", "www.google-analytics.com"]}, {"offerId": "2610314", "title": "Time Master", "status": "success", "postbackUrl": "https://api.mychips.io/v1.5/redirect/?cid=2610314&rt=2&pid=2469&user_id=f302f488-5f1c-43e6-91cb-4e82f6e21658&adunit_id=0ed871d3-c0e2-4f80-995a-4819039c496c&language=en", "domain": "api.mychips.io", "keyParameters": {"cid": "2610314", "rt": "2", "pid": "2469", "user_id": "f302f488-5f1c-43e6-91cb-4e82f6e21658", "adunit_id": "0ed871d3-c0e2-4f80-995a-4819039c496c", "language": "en"}, "requiredCookies": [{"name": "NID", "domain": ".google.com"}, {"name": "NID", "domain": ".google.com"}, {"name": "NID", "domain": ".google.com"}], "totalPostbacks": 5, "allPostbackDomains": ["api.mychips.io", "trk301.com", "ciao.sng.link", "www.google-analytics.com"]}, {"offerId": "2610443", "title": "Ever Legion", "status": "success", "postbackUrl": "https://api.mychips.io/v1.5/redirect/?cid=2610443&rt=2&pid=2469&user_id=f302f488-5f1c-43e6-91cb-4e82f6e21658&adunit_id=0ed871d3-c0e2-4f80-995a-4819039c496c&language=en", "domain": "api.mychips.io", "keyParameters": {"cid": "2610443", "rt": "2", "pid": "2469", "user_id": "f302f488-5f1c-43e6-91cb-4e82f6e21658", "adunit_id": "0ed871d3-c0e2-4f80-995a-4819039c496c", "language": "en"}, "requiredCookies": [{"name": "NID", "domain": ".google.com"}, {"name": "NID", "domain": ".google.com"}, {"name": "NID", "domain": ".google.com"}], "totalPostbacks": 4, "allPostbackDomains": ["api.mychips.io", "trk301.com", "app.adjust.com", "www.google-analytics.com"]}, {"offerId": "2610567", "title": "Match Solitaire", "status": "success", "postbackUrl": "https://api.mychips.io/v1.5/redirect/?cid=2610567&rt=2&pid=2469&user_id=f302f488-5f1c-43e6-91cb-4e82f6e21658&adunit_id=0ed871d3-c0e2-4f80-995a-4819039c496c&language=en", "domain": "api.mychips.io", "keyParameters": {"cid": "2610567", "rt": "2", "pid": "2469", "user_id": "f302f488-5f1c-43e6-91cb-4e82f6e21658", "adunit_id": "0ed871d3-c0e2-4f80-995a-4819039c496c", "language": "en"}, "requiredCookies": [{"name": "NID", "domain": ".google.com"}, {"name": "NID", "domain": ".google.com"}, {"name": "_gid", "domain": ".play.google.com"}], "totalPostbacks": 5, "allPostbackDomains": ["api.mychips.io", "trk301.com", "ciao.sng.link", "www.google-analytics.com"]}, {"offerId": "2610900", "title": "Credit Karma", "status": "success", "postbackUrl": "https://api.mychips.io/v1.5/redirect/?cid=2610900&rt=2&pid=2469&user_id=f302f488-5f1c-43e6-91cb-4e82f6e21658&adunit_id=0ed871d3-c0e2-4f80-995a-4819039c496c&language=en", "domain": "api.mychips.io", "keyParameters": {"cid": "2610900", "rt": "2", "pid": "2469", "user_id": "f302f488-5f1c-43e6-91cb-4e82f6e21658", "adunit_id": "0ed871d3-c0e2-4f80-995a-4819039c496c", "language": "en"}, "requiredCookies": [{"name": "enc_aff_session_369", "domain": "brownboots.go2cloud.org"}, {"name": "CKSESSID", "domain": ".www.creditkarma.com"}, {"name": "CKSESSID", "domain": ".www.creditkarma.com"}], "totalPostbacks": 8, "allPostbackDomains": ["www.creditkarma.com", "cms.creditkarma.com", "ckgoprod-a.akamaihd.net", "pixel.wp.com", "api.mychips.io", "trk301.com", "brownboots.go2cloud.org"]}, {"offerId": "2610930", "title": "Match Puzzle Journey", "status": "success", "postbackUrl": "https://api.mychips.io/v1.5/redirect/?cid=2610930&rt=2&pid=2469&user_id=f302f488-5f1c-43e6-91cb-4e82f6e21658&adunit_id=0ed871d3-c0e2-4f80-995a-4819039c496c&language=en", "domain": "api.mychips.io", "keyParameters": {"cid": "2610930", "rt": "2", "pid": "2469", "user_id": "f302f488-5f1c-43e6-91cb-4e82f6e21658", "adunit_id": "0ed871d3-c0e2-4f80-995a-4819039c496c", "language": "en"}, "requiredCookies": [{"name": "NID", "domain": ".google.com"}, {"name": "NID", "domain": ".google.com"}, {"name": "_gid", "domain": ".play.google.com"}], "totalPostbacks": 5, "allPostbackDomains": ["api.mychips.io", "trk301.com", "ciao.sng.link", "www.google-analytics.com"]}, {"offerId": "2610934", "title": "POP! Slots™ Vegas Casino Games", "status": "success", "postbackUrl": "https://api.mychips.io/v1.5/redirect/?cid=2610934&rt=2&pid=2469&user_id=f302f488-5f1c-43e6-91cb-4e82f6e21658&adunit_id=0ed871d3-c0e2-4f80-995a-4819039c496c&language=en", "domain": "api.mychips.io", "keyParameters": {"cid": "2610934", "rt": "2", "pid": "2469", "user_id": "f302f488-5f1c-43e6-91cb-4e82f6e21658", "adunit_id": "0ed871d3-c0e2-4f80-995a-4819039c496c", "language": "en"}, "requiredCookies": [{"name": "NID", "domain": ".google.com"}, {"name": "NID", "domain": ".google.com"}, {"name": "_gid", "domain": ".play.google.com"}], "totalPostbacks": 6, "allPostbackDomains": ["api.mychips.io", "trk301.com", "playstudios.sng.link", "www.google-analytics.com"]}, {"offerId": "2611618", "title": "Dragon Farm", "status": "success", "postbackUrl": "https://api.mychips.io/v1.5/redirect/?cid=2611618&rt=2&pid=2469&user_id=f302f488-5f1c-43e6-91cb-4e82f6e21658&adunit_id=0ed871d3-c0e2-4f80-995a-4819039c496c&language=en", "domain": "api.mychips.io", "keyParameters": {"cid": "2611618", "rt": "2", "pid": "2469", "user_id": "f302f488-5f1c-43e6-91cb-4e82f6e21658", "adunit_id": "0ed871d3-c0e2-4f80-995a-4819039c496c", "language": "en"}, "requiredCookies": [{"name": "NID", "domain": ".google.com"}, {"name": "NID", "domain": ".google.com"}, {"name": "NID", "domain": ".google.com"}], "totalPostbacks": 5, "allPostbackDomains": ["api.mychips.io", "trk301.com", "ciao.sng.link", "www.google-analytics.com"]}, {"offerId": "2611750", "title": "MONEY KITTY: Play & Earn Money", "status": "success", "postbackUrl": "https://api.mychips.io/v1.5/redirect/?cid=2611750&rt=2&pid=2469&user_id=f302f488-5f1c-43e6-91cb-4e82f6e21658&adunit_id=0ed871d3-c0e2-4f80-995a-4819039c496c&language=en", "domain": "api.mychips.io", "keyParameters": {"cid": "2611750", "rt": "2", "pid": "2469", "user_id": "f302f488-5f1c-43e6-91cb-4e82f6e21658", "adunit_id": "0ed871d3-c0e2-4f80-995a-4819039c496c", "language": "en"}, "requiredCookies": [{"name": "NID", "domain": ".google.com"}, {"name": "NID", "domain": ".google.com"}, {"name": "NID", "domain": ".google.com"}], "totalPostbacks": 5, "allPostbackDomains": ["api.mychips.io", "trk301.com", "play.sng.link", "www.google-analytics.com"]}, {"offerId": "2611793", "title": "SlotsWise", "status": "success", "postbackUrl": "https://api.mychips.io/v1.5/redirect/?cid=2611793&rt=2&pid=2469&user_id=f302f488-5f1c-43e6-91cb-4e82f6e21658&adunit_id=0ed871d3-c0e2-4f80-995a-4819039c496c&language=en", "domain": "api.mychips.io", "keyParameters": {"cid": "2611793", "rt": "2", "pid": "2469", "user_id": "f302f488-5f1c-43e6-91cb-4e82f6e21658", "adunit_id": "0ed871d3-c0e2-4f80-995a-4819039c496c", "language": "en"}, "requiredCookies": [{"name": "sid", "domain": ".otnetsk.com"}, {"name": "_vwo_uuid_v2", "domain": ".slotswise.com"}, {"name": "_vwo_uuid_v2", "domain": ".slotswise.com"}], "totalPostbacks": 5, "allPostbackDomains": ["api.mychips.io", "trk301.com", "otnetsk.com", "www.us.slotswise.com"]}, {"offerId": "2611966", "title": "PLAYTIME - <PERSON><PERSON><PERSON>", "status": "success", "postbackUrl": "https://api.mychips.io/v1.5/redirect/?cid=2611966&rt=2&pid=2469&user_id=f302f488-5f1c-43e6-91cb-4e82f6e21658&adunit_id=0ed871d3-c0e2-4f80-995a-4819039c496c&language=en", "domain": "api.mychips.io", "keyParameters": {"cid": "2611966", "rt": "2", "pid": "2469", "user_id": "f302f488-5f1c-43e6-91cb-4e82f6e21658", "adunit_id": "0ed871d3-c0e2-4f80-995a-4819039c496c", "language": "en"}, "requiredCookies": [{"name": "NID", "domain": ".google.com"}, {"name": "NID", "domain": ".google.com"}, {"name": "_gid", "domain": ".play.google.com"}], "totalPostbacks": 5, "allPostbackDomains": ["api.mychips.io", "trk301.com", "play.sng.link", "www.google-analytics.com"]}, {"offerId": "2611994", "title": "Raid", "status": "success", "postbackUrl": "https://api.mychips.io/v1.5/redirect/?cid=2611994&rt=2&pid=2469&user_id=f302f488-5f1c-43e6-91cb-4e82f6e21658&adunit_id=0ed871d3-c0e2-4f80-995a-4819039c496c&language=en", "domain": "api.mychips.io", "keyParameters": {"cid": "2611994", "rt": "2", "pid": "2469", "user_id": "f302f488-5f1c-43e6-91cb-4e82f6e21658", "adunit_id": "0ed871d3-c0e2-4f80-995a-4819039c496c", "language": "en"}, "requiredCookies": [{"name": "or_id.v1", "domain": ".plarium.com"}, {"name": "a_uid", "domain": "lps.plarium.com"}, {"name": "or_id.v1", "domain": ".plarium.com"}], "totalPostbacks": 10, "allPostbackDomains": ["analytics.google.com", "googleads.g.doubleclick.net", "www.google.com", "td.doubleclick.net", "api.mychips.io", "trk301.com", "stats.g.doubleclick.net"]}, {"offerId": "2612109", "title": "Word Wise", "status": "success", "postbackUrl": "https://api.mychips.io/v1.5/redirect/?cid=2612109&rt=2&pid=2469&user_id=f302f488-5f1c-43e6-91cb-4e82f6e21658&adunit_id=0ed871d3-c0e2-4f80-995a-4819039c496c&language=en", "domain": "api.mychips.io", "keyParameters": {"cid": "2612109", "rt": "2", "pid": "2469", "user_id": "f302f488-5f1c-43e6-91cb-4e82f6e21658", "adunit_id": "0ed871d3-c0e2-4f80-995a-4819039c496c", "language": "en"}, "requiredCookies": [{"name": "NID", "domain": ".google.com"}, {"name": "NID", "domain": ".google.com"}, {"name": "_gid", "domain": ".play.google.com"}], "totalPostbacks": 5, "allPostbackDomains": ["api.mychips.io", "trk301.com", "ciao.sng.link", "www.google-analytics.com"]}, {"offerId": "2612173", "title": "Revolut", "status": "success", "postbackUrl": "https://api.mychips.io/v1.5/redirect/?cid=2612173&rt=2&pid=2469&user_id=f302f488-5f1c-43e6-91cb-4e82f6e21658&adunit_id=0ed871d3-c0e2-4f80-995a-4819039c496c&language=en", "domain": "api.mychips.io", "keyParameters": {"cid": "2612173", "rt": "2", "pid": "2469", "user_id": "f302f488-5f1c-43e6-91cb-4e82f6e21658", "adunit_id": "0ed871d3-c0e2-4f80-995a-4819039c496c", "language": "en"}, "requiredCookies": [{"name": "rev_cid", "domain": ".revolut.com"}, {"name": "_cfuvid", "domain": ".revolut.com"}, {"name": "rev_cid", "domain": ".revolut.com"}], "totalPostbacks": 5, "allPostbackDomains": ["api.mychips.io", "trk301.com", "revolut.onelink.me", "www.revolut.com"]}, {"offerId": "2612221", "title": "The Farmers", "status": "success", "postbackUrl": "https://api.mychips.io/v1.5/redirect/?cid=2612221&rt=2&pid=2469&user_id=f302f488-5f1c-43e6-91cb-4e82f6e21658&adunit_id=0ed871d3-c0e2-4f80-995a-4819039c496c&language=en", "domain": "api.mychips.io", "keyParameters": {"cid": "2612221", "rt": "2", "pid": "2469", "user_id": "f302f488-5f1c-43e6-91cb-4e82f6e21658", "adunit_id": "0ed871d3-c0e2-4f80-995a-4819039c496c", "language": "en"}, "requiredCookies": [{"name": "NID", "domain": ".google.com"}, {"name": "NID", "domain": ".google.com"}, {"name": "NID", "domain": ".google.com"}], "totalPostbacks": 5, "allPostbackDomains": ["api.mychips.io", "trk301.com", "ciao.sng.link", "www.google-analytics.com"]}, {"offerId": "2612235", "title": "Daily Goodie box", "status": "success", "postbackUrl": "https://api.mychips.io/v1.5/redirect/?cid=2612235&rt=2&pid=2469&user_id=f302f488-5f1c-43e6-91cb-4e82f6e21658&adunit_id=0ed871d3-c0e2-4f80-995a-4819039c496c&language=en", "domain": "api.mychips.io", "keyParameters": {"cid": "2612235", "rt": "2", "pid": "2469", "user_id": "f302f488-5f1c-43e6-91cb-4e82f6e21658", "adunit_id": "0ed871d3-c0e2-4f80-995a-4819039c496c", "language": "en"}, "requiredCookies": [{"name": "enc_aff_session_15", "domain": "brownboots.go2cloud.org"}, {"name": "enc_aff_session_7581", "domain": "la.luxeads.com"}, {"name": "slim_session", "domain": "dailygoodiebox.com"}], "totalPostbacks": 10, "allPostbackDomains": ["connect.facebook.net", "td.doubleclick.net", "googleads.g.doubleclick.net", "www.google.com", "api.mychips.io", "trk301.com", "brownboots.go2cloud.org", "la.luxeads.com", "dailygoodiebox.com"]}, {"offerId": "2612267", "title": "<PERSON><PERSON><PERSON><PERSON>", "status": "success", "postbackUrl": "https://api.mychips.io/v1.5/redirect/?cid=2612267&rt=2&pid=2469&user_id=f302f488-5f1c-43e6-91cb-4e82f6e21658&adunit_id=0ed871d3-c0e2-4f80-995a-4819039c496c&language=en", "domain": "api.mychips.io", "keyParameters": {"cid": "2612267", "rt": "2", "pid": "2469", "user_id": "f302f488-5f1c-43e6-91cb-4e82f6e21658", "adunit_id": "0ed871d3-c0e2-4f80-995a-4819039c496c", "language": "en"}, "requiredCookies": [{"name": "initialTrafficSource", "domain": ".mcluck.com"}, {"name": "lastTrafficSource", "domain": ".mcluck.com"}, {"name": "initialTrafficSource", "domain": ".mcluck.com"}], "totalPostbacks": 48, "allPostbackDomains": ["tracking.b2-partners.com", "www.payment-mf-static.mcluck.com", "analytics.tiktok.com", "cdn.jsdelivr.net", "connect.facebook.net", "www.redditstatic.com", "lh.trafficguard.ai", "sc-static.net", "logx.optimizely.com", "www.google.com", "trc.taboola.com", "pixel-config.reddit.com", "alb.reddit.com", "tracker.ads.sportradar.com", "tr.snapchat.com", "www.google-analytics.com", "a.sportradarserving.com", "api.trafficguard.ai", "pixel.tapad.com", "api.mychips.io", "trk301.com", "stats.g.doubleclick.net", "analytics.google.com", "sync.intentiq.com", "x.bidswitch.net", "syncv4.intentiq.com", "dsum.casalemedia.com", "ad.360yield.com"]}, {"offerId": "2612334", "title": "Empower: Cash Advance & Credit", "status": "success", "postbackUrl": "https://api.mychips.io/v1.5/redirect/?cid=2612334&rt=2&pid=2469&user_id=f302f488-5f1c-43e6-91cb-4e82f6e21658&adunit_id=0ed871d3-c0e2-4f80-995a-4819039c496c&language=en", "domain": "api.mychips.io", "keyParameters": {"cid": "2612334", "rt": "2", "pid": "2469", "user_id": "f302f488-5f1c-43e6-91cb-4e82f6e21658", "adunit_id": "0ed871d3-c0e2-4f80-995a-4819039c496c", "language": "en"}, "requiredCookies": [], "totalPostbacks": 2, "allPostbackDomains": ["api.mychips.io", "trk301.com"]}, {"offerId": "2612334", "title": "Empower: Cash Advance & Credit", "status": "success", "postbackUrl": "https://api.mychips.io/v1.5/redirect/?cid=2612334&rt=2&pid=2469&user_id=f302f488-5f1c-43e6-91cb-4e82f6e21658&adunit_id=0ed871d3-c0e2-4f80-995a-4819039c496c&language=en", "domain": "api.mychips.io", "keyParameters": {"cid": "2612334", "rt": "2", "pid": "2469", "user_id": "f302f488-5f1c-43e6-91cb-4e82f6e21658", "adunit_id": "0ed871d3-c0e2-4f80-995a-4819039c496c", "language": "en"}, "requiredCookies": [], "totalPostbacks": 2, "allPostbackDomains": ["api.mychips.io", "trk301.com"]}, {"offerId": "2612410", "title": "Block Puzzle : Match Combo", "status": "success", "postbackUrl": "https://api.mychips.io/v1.5/redirect/?cid=2612410&rt=2&pid=2469&user_id=f302f488-5f1c-43e6-91cb-4e82f6e21658&adunit_id=0ed871d3-c0e2-4f80-995a-4819039c496c&language=en", "domain": "api.mychips.io", "keyParameters": {"cid": "2612410", "rt": "2", "pid": "2469", "user_id": "f302f488-5f1c-43e6-91cb-4e82f6e21658", "adunit_id": "0ed871d3-c0e2-4f80-995a-4819039c496c", "language": "en"}, "requiredCookies": [{"name": "NID", "domain": ".google.com"}, {"name": "NID", "domain": ".google.com"}, {"name": "NID", "domain": ".google.com"}], "totalPostbacks": 5, "allPostbackDomains": ["api.mychips.io", "trk301.com", "actionfit.sng.link", "www.google-analytics.com"]}, {"offerId": "2612568", "title": "myVEGAS Slots – Casino Slots", "status": "success", "postbackUrl": "https://api.mychips.io/v1.5/redirect/?cid=2612568&rt=2&pid=2469&user_id=f302f488-5f1c-43e6-91cb-4e82f6e21658&adunit_id=0ed871d3-c0e2-4f80-995a-4819039c496c&language=en", "domain": "api.mychips.io", "keyParameters": {"cid": "2612568", "rt": "2", "pid": "2469", "user_id": "f302f488-5f1c-43e6-91cb-4e82f6e21658", "adunit_id": "0ed871d3-c0e2-4f80-995a-4819039c496c", "language": "en"}, "requiredCookies": [], "totalPostbacks": 4, "allPostbackDomains": ["api.mychips.io", "trk301.com", "playstudios.sng.link"]}, {"offerId": "2612578", "title": "SpongeBob Adventures: In A Jam", "status": "success", "postbackUrl": "https://api.mychips.io/v1.5/redirect/?cid=2612578&rt=2&pid=2469&user_id=f302f488-5f1c-43e6-91cb-4e82f6e21658&adunit_id=0ed871d3-c0e2-4f80-995a-4819039c496c&preview=1&language=en", "domain": "api.mychips.io", "keyParameters": {"cid": "2612578", "rt": "2", "pid": "2469", "user_id": "f302f488-5f1c-43e6-91cb-4e82f6e21658", "adunit_id": "0ed871d3-c0e2-4f80-995a-4819039c496c", "preview": "1", "language": "en"}, "requiredCookies": [{"name": "NID", "domain": ".google.com"}, {"name": "NID", "domain": ".google.com"}, {"name": "_gid", "domain": ".play.google.com"}], "totalPostbacks": 3, "allPostbackDomains": ["api.mychips.io", "trk301.com", "www.google-analytics.com"]}, {"offerId": "2612578", "title": "SpongeBob Adventures: In A Jam", "status": "success", "postbackUrl": "https://api.mychips.io/v1.5/redirect/?cid=2612578&rt=2&pid=2469&user_id=f302f488-5f1c-43e6-91cb-4e82f6e21658&adunit_id=0ed871d3-c0e2-4f80-995a-4819039c496c&preview=1&language=en", "domain": "api.mychips.io", "keyParameters": {"cid": "2612578", "rt": "2", "pid": "2469", "user_id": "f302f488-5f1c-43e6-91cb-4e82f6e21658", "adunit_id": "0ed871d3-c0e2-4f80-995a-4819039c496c", "preview": "1", "language": "en"}, "requiredCookies": [{"name": "NID", "domain": ".google.com"}, {"name": "NID", "domain": ".google.com"}, {"name": "NID", "domain": ".google.com"}], "totalPostbacks": 3, "allPostbackDomains": ["api.mychips.io", "trk301.com", "www.google-analytics.com"]}, {"offerId": "2612641", "title": "Merge <PERSON>", "status": "success", "postbackUrl": "https://api.mychips.io/v1.5/redirect/?cid=2612641&rt=2&pid=2469&user_id=f302f488-5f1c-43e6-91cb-4e82f6e21658&adunit_id=0ed871d3-c0e2-4f80-995a-4819039c496c&language=en", "domain": "api.mychips.io", "keyParameters": {"cid": "2612641", "rt": "2", "pid": "2469", "user_id": "f302f488-5f1c-43e6-91cb-4e82f6e21658", "adunit_id": "0ed871d3-c0e2-4f80-995a-4819039c496c", "language": "en"}, "requiredCookies": [{"name": "NID", "domain": ".google.com"}, {"name": "NID", "domain": ".google.com"}, {"name": "NID", "domain": ".google.com"}], "totalPostbacks": 5, "allPostbackDomains": ["api.mychips.io", "trk301.com", "ciao.sng.link", "www.google-analytics.com"]}, {"offerId": "2612706", "title": "Chime – Mobile Banking", "status": "success", "postbackUrl": "https://api.mychips.io/v1.5/redirect/?cid=2612706&rt=2&pid=2469&user_id=f302f488-5f1c-43e6-91cb-4e82f6e21658&adunit_id=0ed871d3-c0e2-4f80-995a-4819039c496c&language=en", "domain": "api.mychips.io", "keyParameters": {"cid": "2612706", "rt": "2", "pid": "2469", "user_id": "f302f488-5f1c-43e6-91cb-4e82f6e21658", "adunit_id": "0ed871d3-c0e2-4f80-995a-4819039c496c", "language": "en"}, "requiredCookies": [{"name": "_cfuvid", "domain": ".chime.com"}, {"name": "_cfuvid", "domain": ".chime.com"}, {"name": "_cfuvid", "domain": ".chime.com"}], "totalPostbacks": 48, "allPostbackDomains": ["www.chime.com", "sc-static.net", "analytics.tiktok.com", "feedmob-cdn.s3.amazonaws.com", "googleads.g.doubleclick.net", "trc.taboola.com", "aa.agkn.com", "www.googleadservices.com", "tr.snapchat.com", "pixel-api.feedmob.biz", "www.google.com", "verifi.podscribe.com", "pixel.tapad.com", "www.redditstatic.com", "s.axon.ai", "d2hrivdxn8ekm8.cloudfront.net", "connect.facebook.net", "td.doubleclick.net", "alb.reddit.com", "sitepixel.blis.com", "pixel-config.reddit.com", "www.knotch-cdn.com", "tr.outbrain.com", "b.applovin.com", "api.mychips.io", "trk301.com", "www.ojrq.net", "sync.intentiq.com", "syncv4.intentiq.com", "stats.g.doubleclick.net", "analytics.google.com"]}, {"offerId": "2612707", "title": "Chime - Mobile Banking", "status": "success", "postbackUrl": "https://api.mychips.io/v1.5/redirect/?cid=2612707&rt=2&pid=2469&user_id=f302f488-5f1c-43e6-91cb-4e82f6e21658&adunit_id=0ed871d3-c0e2-4f80-995a-4819039c496c&language=en", "domain": "api.mychips.io", "keyParameters": {"cid": "2612707", "rt": "2", "pid": "2469", "user_id": "f302f488-5f1c-43e6-91cb-4e82f6e21658", "adunit_id": "0ed871d3-c0e2-4f80-995a-4819039c496c", "language": "en"}, "requiredCookies": [{"name": "_cfuvid", "domain": ".chime.com"}, {"name": "_cfuvid", "domain": ".chime.com"}, {"name": "_cfuvid", "domain": ".chime.com"}], "totalPostbacks": 48, "allPostbackDomains": ["www.chime.com", "sc-static.net", "analytics.tiktok.com", "trc.taboola.com", "feedmob-cdn.s3.amazonaws.com", "aa.agkn.com", "tr.snapchat.com", "googleads.g.doubleclick.net", "www.googleadservices.com", "pixel-api.feedmob.biz", "verifi.podscribe.com", "www.google.com", "pixel.tapad.com", "www.redditstatic.com", "s.axon.ai", "td.doubleclick.net", "connect.facebook.net", "sitepixel.blis.com", "d2hrivdxn8ekm8.cloudfront.net", "pixel-config.reddit.com", "www.knotch-cdn.com", "alb.reddit.com", "b.applovin.com", "tr.outbrain.com", "api.mychips.io", "trk301.com", "www.ojrq.net", "sync.intentiq.com", "syncv4.intentiq.com", "analytics.google.com", "stats.g.doubleclick.net"]}, {"offerId": "2612852", "title": "Word Madness", "status": "success", "postbackUrl": "https://api.mychips.io/v1.5/redirect/?cid=2612852&rt=2&pid=2469&user_id=f302f488-5f1c-43e6-91cb-4e82f6e21658&adunit_id=0ed871d3-c0e2-4f80-995a-4819039c496c&language=en", "domain": "api.mychips.io", "keyParameters": {"cid": "2612852", "rt": "2", "pid": "2469", "user_id": "f302f488-5f1c-43e6-91cb-4e82f6e21658", "adunit_id": "0ed871d3-c0e2-4f80-995a-4819039c496c", "language": "en"}, "requiredCookies": [{"name": "NID", "domain": ".google.com"}, {"name": "NID", "domain": ".google.com"}, {"name": "NID", "domain": ".google.com"}], "totalPostbacks": 6, "allPostbackDomains": ["api.mychips.io", "trk301.com", "wm.sng.link", "www.google-analytics.com"]}, {"offerId": "2612866", "title": "Epic Blast 3D", "status": "success", "postbackUrl": "https://api.mychips.io/v1.5/redirect/?cid=2612866&rt=2&pid=2469&user_id=f302f488-5f1c-43e6-91cb-4e82f6e21658&adunit_id=0ed871d3-c0e2-4f80-995a-4819039c496c&language=en", "domain": "api.mychips.io", "keyParameters": {"cid": "2612866", "rt": "2", "pid": "2469", "user_id": "f302f488-5f1c-43e6-91cb-4e82f6e21658", "adunit_id": "0ed871d3-c0e2-4f80-995a-4819039c496c", "language": "en"}, "requiredCookies": [{"name": "NID", "domain": ".google.com"}, {"name": "NID", "domain": ".google.com"}, {"name": "NID", "domain": ".google.com"}], "totalPostbacks": 5, "allPostbackDomains": ["api.mychips.io", "trk301.com", "ciao.sng.link", "www.google-analytics.com"]}, {"offerId": "2612918", "title": "Gravy pass", "status": "success", "postbackUrl": "https://api.mychips.io/v1.5/redirect/?cid=2612918&rt=2&pid=2469&user_id=f302f488-5f1c-43e6-91cb-4e82f6e21658&adunit_id=0ed871d3-c0e2-4f80-995a-4819039c496c&language=en", "domain": "api.mychips.io", "keyParameters": {"cid": "2612918", "rt": "2", "pid": "2469", "user_id": "f302f488-5f1c-43e6-91cb-4e82f6e21658", "adunit_id": "0ed871d3-c0e2-4f80-995a-4819039c496c", "language": "en"}, "requiredCookies": [{"name": "NID", "domain": ".google.com"}, {"name": "NID", "domain": ".google.com"}, {"name": "NID", "domain": ".google.com"}], "totalPostbacks": 2, "allPostbackDomains": ["api.mychips.io", "trk301.com"]}, {"offerId": "2613544", "title": "Water Sort Master 3D", "status": "success", "postbackUrl": "https://api.mychips.io/v1.5/redirect/?cid=2613544&rt=2&pid=2469&user_id=f302f488-5f1c-43e6-91cb-4e82f6e21658&adunit_id=0ed871d3-c0e2-4f80-995a-4819039c496c&language=en", "domain": "api.mychips.io", "keyParameters": {"cid": "2613544", "rt": "2", "pid": "2469", "user_id": "f302f488-5f1c-43e6-91cb-4e82f6e21658", "adunit_id": "0ed871d3-c0e2-4f80-995a-4819039c496c", "language": "en"}, "requiredCookies": [{"name": "NID", "domain": ".google.com"}, {"name": "NID", "domain": ".google.com"}, {"name": "NID", "domain": ".google.com"}], "totalPostbacks": 5, "allPostbackDomains": ["api.mychips.io", "trk301.com", "ciao.sng.link", "www.google-analytics.com"]}, {"offerId": "2613587", "title": "We Are Warriors!", "status": "success", "postbackUrl": "https://api.mychips.io/v1.5/redirect/?cid=2613587&rt=2&pid=2469&user_id=f302f488-5f1c-43e6-91cb-4e82f6e21658&adunit_id=0ed871d3-c0e2-4f80-995a-4819039c496c&preview=1&language=en", "domain": "api.mychips.io", "keyParameters": {"cid": "2613587", "rt": "2", "pid": "2469", "user_id": "f302f488-5f1c-43e6-91cb-4e82f6e21658", "adunit_id": "0ed871d3-c0e2-4f80-995a-4819039c496c", "preview": "1", "language": "en"}, "requiredCookies": [{"name": "NID", "domain": ".google.com"}, {"name": "NID", "domain": ".google.com"}, {"name": "_gid", "domain": ".play.google.com"}], "totalPostbacks": 3, "allPostbackDomains": ["api.mychips.io", "trk301.com", "www.google-analytics.com"]}, {"offerId": "2613670", "title": "Stash", "status": "success", "postbackUrl": "https://api.mychips.io/v1.5/redirect/?cid=2613670&rt=2&pid=2469&user_id=f302f488-5f1c-43e6-91cb-4e82f6e21658&adunit_id=0ed871d3-c0e2-4f80-995a-4819039c496c&language=en", "domain": "api.mychips.io", "keyParameters": {"cid": "2613670", "rt": "2", "pid": "2469", "user_id": "f302f488-5f1c-43e6-91cb-4e82f6e21658", "adunit_id": "0ed871d3-c0e2-4f80-995a-4819039c496c", "language": "en"}, "requiredCookies": [{"name": "ASPSESSIONIDAAEBDRQS", "domain": "www.searchcactus.com"}, {"name": "ASPSESSIONIDAAEBDRQS", "domain": "www.searchcactus.com"}, {"name": "upmid", "domain": "www.searchcactus.com"}], "totalPostbacks": 4, "allPostbackDomains": ["www.searchcactus.com", "api.mychips.io", "trk301.com"]}, {"offerId": "2613672", "title": "Mining Empire Idle", "status": "success", "postbackUrl": "https://api.mychips.io/v1.5/redirect/?cid=2613672&rt=2&pid=2469&user_id=f302f488-5f1c-43e6-91cb-4e82f6e21658&adunit_id=0ed871d3-c0e2-4f80-995a-4819039c496c&language=en", "domain": "api.mychips.io", "keyParameters": {"cid": "2613672", "rt": "2", "pid": "2469", "user_id": "f302f488-5f1c-43e6-91cb-4e82f6e21658", "adunit_id": "0ed871d3-c0e2-4f80-995a-4819039c496c", "language": "en"}, "requiredCookies": [{"name": "NID", "domain": ".google.com"}, {"name": "NID", "domain": ".google.com"}, {"name": "NID", "domain": ".google.com"}], "totalPostbacks": 5, "allPostbackDomains": ["api.mychips.io", "trk301.com", "ciao.sng.link", "www.google-analytics.com"]}, {"offerId": "2613813", "title": "Coinbase", "status": "success", "postbackUrl": "https://api.mychips.io/v1.5/redirect/?cid=2613813&rt=2&pid=2469&user_id=f302f488-5f1c-43e6-91cb-4e82f6e21658&adunit_id=0ed871d3-c0e2-4f80-995a-4819039c496c&language=en", "domain": "api.mychips.io", "keyParameters": {"cid": "2613813", "rt": "2", "pid": "2469", "user_id": "f302f488-5f1c-43e6-91cb-4e82f6e21658", "adunit_id": "0ed871d3-c0e2-4f80-995a-4819039c496c", "language": "en"}, "requiredCookies": [{"name": "afclick", "domain": "appglowing.g2afse.com"}, {"name": "coinbase_device_id", "domain": ".coinbase.com"}, {"name": "_cfuvid", "domain": ".coinbase.com"}], "totalPostbacks": 3, "allPostbackDomains": ["api.mychips.io", "trk301.com", "www.ojrq.net"]}, {"offerId": "2613817", "title": "Bus Frenzy - Traffic Jam", "status": "success", "postbackUrl": "https://api.mychips.io/v1.5/redirect/?cid=2613817&rt=2&pid=2469&user_id=f302f488-5f1c-43e6-91cb-4e82f6e21658&adunit_id=0ed871d3-c0e2-4f80-995a-4819039c496c&preview=1&language=en", "domain": "api.mychips.io", "keyParameters": {"cid": "2613817", "rt": "2", "pid": "2469", "user_id": "f302f488-5f1c-43e6-91cb-4e82f6e21658", "adunit_id": "0ed871d3-c0e2-4f80-995a-4819039c496c", "preview": "1", "language": "en"}, "requiredCookies": [{"name": "NID", "domain": ".google.com"}, {"name": "NID", "domain": ".google.com"}, {"name": "_gid", "domain": ".play.google.com"}], "totalPostbacks": 3, "allPostbackDomains": ["api.mychips.io", "trk301.com", "www.google-analytics.com"]}, {"offerId": "2613859", "title": "Blockdown Adventures", "status": "success", "postbackUrl": "https://api.mychips.io/v1.5/redirect/?cid=2613859&rt=2&pid=2469&user_id=f302f488-5f1c-43e6-91cb-4e82f6e21658&adunit_id=0ed871d3-c0e2-4f80-995a-4819039c496c&language=en", "domain": "api.mychips.io", "keyParameters": {"cid": "2613859", "rt": "2", "pid": "2469", "user_id": "f302f488-5f1c-43e6-91cb-4e82f6e21658", "adunit_id": "0ed871d3-c0e2-4f80-995a-4819039c496c", "language": "en"}, "requiredCookies": [{"name": "NID", "domain": ".google.com"}, {"name": "NID", "domain": ".google.com"}, {"name": "_gid", "domain": ".play.google.com"}], "totalPostbacks": 5, "allPostbackDomains": ["api.mychips.io", "trk301.com", "ciao.sng.link", "www.google-analytics.com"]}, {"offerId": "2613977", "title": "DoorDash - <PERSON><PERSON>", "status": "success", "postbackUrl": "https://api.mychips.io/v1.5/redirect/?cid=2613977&rt=2&pid=2469&user_id=f302f488-5f1c-43e6-91cb-4e82f6e21658&adunit_id=0ed871d3-c0e2-4f80-995a-4819039c496c&language=en", "domain": "api.mychips.io", "keyParameters": {"cid": "2613977", "rt": "2", "pid": "2469", "user_id": "f302f488-5f1c-43e6-91cb-4e82f6e21658", "adunit_id": "0ed871d3-c0e2-4f80-995a-4819039c496c", "language": "en"}, "requiredCookies": [{"name": "_cfuvid", "domain": ".www.doordash.com"}, {"name": "_cfuvid", "domain": ".doordash.com"}, {"name": "_cfuvid", "domain": ".doordash.com"}], "totalPostbacks": 3, "allPostbackDomains": ["api.mychips.io", "trk301.com", "www.ojrq.net"]}, {"offerId": "2613978", "title": "Dream Restaurant - Hotel games", "status": "success", "postbackUrl": "https://api.mychips.io/v1.5/redirect/?cid=2613978&rt=2&pid=2469&user_id=f302f488-5f1c-43e6-91cb-4e82f6e21658&adunit_id=0ed871d3-c0e2-4f80-995a-4819039c496c&language=en", "domain": "api.mychips.io", "keyParameters": {"cid": "2613978", "rt": "2", "pid": "2469", "user_id": "f302f488-5f1c-43e6-91cb-4e82f6e21658", "adunit_id": "0ed871d3-c0e2-4f80-995a-4819039c496c", "language": "en"}, "requiredCookies": [{"name": "NID", "domain": ".google.com"}, {"name": "NID", "domain": ".google.com"}, {"name": "NID", "domain": ".google.com"}], "totalPostbacks": 5, "allPostbackDomains": ["api.mychips.io", "trk301.com", "ciao.sng.link", "www.google-analytics.com"]}, {"offerId": "2613984", "title": "Merge Cruise: Mystery Puzzle", "status": "success", "postbackUrl": "https://api.mychips.io/v1.5/redirect/?cid=2613984&rt=2&pid=2469&user_id=f302f488-5f1c-43e6-91cb-4e82f6e21658&adunit_id=0ed871d3-c0e2-4f80-995a-4819039c496c&language=en", "domain": "api.mychips.io", "keyParameters": {"cid": "2613984", "rt": "2", "pid": "2469", "user_id": "f302f488-5f1c-43e6-91cb-4e82f6e21658", "adunit_id": "0ed871d3-c0e2-4f80-995a-4819039c496c", "language": "en"}, "requiredCookies": [{"name": "client-session-bind", "domain": "www.peerplay.com"}, {"name": "server-session-bind", "domain": "www.peerplay.com"}, {"name": "server-session-bind", "domain": "www.peerplay.com"}], "totalPostbacks": 14, "allPostbackDomains": ["siteassets.parastorage.com", "frog.wix.com", "td.doubleclick.net", "googleads.g.doubleclick.net", "www.google.com", "api.mychips.io", "trk301.com", "peerplay.sng.link", "www.google-analytics.com"]}]