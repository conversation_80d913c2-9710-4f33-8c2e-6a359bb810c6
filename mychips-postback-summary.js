const fs = require('fs');

function createMychipsPostbackSummary() {
  try {
    console.log('📖 Reading MyChips postback analysis...');
    const data = JSON.parse(fs.readFileSync('mychips_postback_analysis.json', 'utf8'));
    
    const summary = [];
    
    data.forEach(offer => {
      const offerId = offer.offerId;
      const title = offer.title;
      
      // Find the main postback URL (usually the API or tracking URL)
      let mainPostback = null;
      
      // Look for MyChips API URLs first (these are usually the main postbacks)
      const apiPostbacks = offer.postbackUrls.filter(url => 
        url.includes('api.mychips.io') || url.includes('mychips.io')
      );
      
      // Look for tracking URLs
      const trackingPostbacks = offer.postbackUrls.filter(url => 
        url.includes('trk') && !url.includes('google') && !url.includes('facebook')
      );
      
      // Look for other postback-like URLs
      const otherPostbacks = offer.postbackUrls.filter(url => {
        const urlLower = url.toLowerCase();
        return !urlLower.includes('google') &&
               !urlLower.includes('facebook') &&
               !urlLower.includes('doubleclick') &&
               !urlLower.includes('analytics') &&
               !urlLower.includes('.js') &&
               !urlLower.includes('.css') &&
               (urlLower.includes('postback') ||
                urlLower.includes('callback') ||
                urlLower.includes('conversion') ||
                url.includes('click_id=') ||
                url.includes('transaction_id=') ||
                url.includes('cid=') ||
                url.includes('user_id='));
      });
      
      // Priority: API > Tracking > Other postbacks
      if (apiPostbacks.length > 0) {
        mainPostback = apiPostbacks[0];
      } else if (trackingPostbacks.length > 0) {
        mainPostback = trackingPostbacks[0];
      } else if (otherPostbacks.length > 0) {
        mainPostback = otherPostbacks[0];
      }
      
      if (mainPostback) {
        try {
          const url = new URL(mainPostback);
          const domain = url.hostname;
          
          // Extract key parameters
          const keyParams = {};
          const importantParamNames = [
            'cid', 'rt', 'pid', 'user_id', 'adunit_id', 'preview', 'language',
            'offer_id', 'aff_id', 'click_id', 'transaction_id', 'conversion_id',
            'aff_sub', 'aff_unique1', 'aff_click_id', 's1', 's2', 's3', 's4', 's5',
            'sub_id', 'payout', 'revenue', 'a', 'c', 'rid'
          ];
          
          importantParamNames.forEach(param => {
            if (url.searchParams.has(param)) {
              keyParams[param] = url.searchParams.get(param);
            }
          });
          
          // Extract relevant cookies (limit to most important ones)
          const keyCookies = offer.relevantCookies.filter(cookie => {
            const name = cookie.name.toLowerCase();
            return name.includes('session') ||
                   name.includes('click') ||
                   name.includes('aff') ||
                   name.includes('track') ||
                   name.includes('nid') ||
                   name.includes('sid') ||
                   name.includes('id');
          }).slice(0, 3); // Limit to top 3 most relevant
          
          summary.push({
            offerId: offerId,
            title: title,
            status: offer.status,
            postbackUrl: mainPostback,
            domain: domain,
            keyParameters: keyParams,
            requiredCookies: keyCookies.map(c => ({
              name: c.name,
              domain: c.domain
            })),
            totalPostbacks: offer.postbackUrls.length,
            allPostbackDomains: [...new Set(offer.postbackUrls.map(url => {
              try {
                return new URL(url).hostname;
              } catch (e) {
                return 'invalid-url';
              }
            }))]
          });
        } catch (e) {
          console.log(`⚠️  Could not parse URL for MyChips offer ${offerId}`);
        }
      }
    });
    
    // Sort by offer ID
    summary.sort((a, b) => {
      const aId = typeof a.offerId === 'string' ? a.offerId : String(a.offerId);
      const bId = typeof b.offerId === 'string' ? b.offerId : String(b.offerId);
      return aId.localeCompare(bId);
    });
    
    // Save summary
    fs.writeFileSync('mychips_postback_summary.json', JSON.stringify(summary, null, 2));
    
    console.log(`✅ Created MyChips summary for ${summary.length} offers with postbacks`);
    console.log(`💾 Saved to: mychips_postback_summary.json`);
    
    // Show first few examples
    console.log('\n📋 Sample MyChips postbacks:');
    summary.slice(0, 5).forEach(offer => {
      console.log(`\n🎯 Offer ${offer.offerId}:`);
      console.log(`   Title: ${offer.title}`);
      console.log(`   Domain: ${offer.domain}`);
      console.log(`   URL: ${offer.postbackUrl.substring(0, 100)}...`);
      if (Object.keys(offer.keyParameters).length > 0) {
        console.log(`   Key params: ${Object.keys(offer.keyParameters).join(', ')}`);
      }
      if (offer.requiredCookies.length > 0) {
        console.log(`   Required cookies: ${offer.requiredCookies.map(c => c.name).join(', ')}`);
      }
      if (offer.allPostbackDomains.length > 1) {
        console.log(`   All domains: ${offer.allPostbackDomains.join(', ')}`);
      }
    });
    
    if (summary.length > 5) {
      console.log(`\n... and ${summary.length - 5} more offers`);
    }
    
    // Show domain statistics
    const domainCounts = {};
    summary.forEach(offer => {
      domainCounts[offer.domain] = (domainCounts[offer.domain] || 0) + 1;
    });
    
    console.log('\n📊 Most common postback domains:');
    Object.entries(domainCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .forEach(([domain, count]) => {
        console.log(`   ${domain}: ${count} offers`);
      });
    
  } catch (error) {
    console.error('❌ Error creating MyChips summary:', error.message);
  }
}

// Run the summary creation
console.log('📝 Creating MyChips postback summary...\n');
createMychipsPostbackSummary();
console.log('\n✨ MyChips summary completed!');
