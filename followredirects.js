// followRedirects.js
const fs = require('fs');
const path = require('path');
const { chromium } = require('playwright');

const OFFERS_PATH = path.join(__dirname, 'Mychipsoffers.json');
const OUTPUT_PATH = path.join(__dirname, 'redirectResults.json');

const androidUA =
  'Mozilla/5.0 (Linux; Android 11; SM-G991U) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.5735.196 Mobile Safari/537.36';
const iosUA =
  'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15A372 Safari/604.1';

async function visitWithUserAgent(browser, offer, userAgent) {
  const context = await browser.newContext({ userAgent });
  const page = await context.newPage();
  const results = {
    id: offer.id,
    title: offer.title,
    attemptedWith: userAgent.includes('Android') ? 'Android' : 'iOS',
    status: 'unknown',
    redirectChain: [],
  };

  // Track visited URLs to avoid duplicates
  const visitedURLs = new Set();

  try {
    page.on('response', async (response) => {
      const url = response.url();

      // Only add unique URLs
      if (!visitedURLs.has(url)) {
        visitedURLs.add(url);
        const cookies = await context.cookies(url);
        results.redirectChain.push({ url, cookies });
      }
    });

    await page.goto(offer.trackingUrl, { waitUntil: 'load', timeout: 15000 });
    results.status = 'success';
  } catch (err) {
    results.status = 'failed';
    results.error = err.message;
  } finally {
    await page.close();
    await context.close();
  }

  return results;
}

(async () => {
  const offers = JSON.parse(fs.readFileSync(OFFERS_PATH, 'utf-8'));
  const finalResults = [];

  const browser = await chromium.launch({ headless: true });

  for (const offer of offers) {
    console.log(`\u23F3 Processing offer: ${offer.title} (${offer.id})`);

    let androidResult = await visitWithUserAgent(browser, offer, androidUA);

    if (
      androidResult.status === 'success' &&
      androidResult.redirectChain.some((r) =>
        r.url.includes('itunes.apple.com') || r.url.includes('apps.apple.com')
      )
    ) {
      console.log(`\u26A0\uFE0F Apple redirect detected. Retrying with iOS.`);
      let iosResult = await visitWithUserAgent(browser, offer, iosUA);
      iosResult.attemptedWith = 'Android, iOS';
      finalResults.push(iosResult);
    } else {
      finalResults.push(androidResult);
    }

    // Delay between offers (adjust as needed)
    await new Promise((res) => setTimeout(res, 4000));
  }

  await browser.close();

  fs.writeFileSync(OUTPUT_PATH, JSON.stringify(finalResults, null, 2));
  console.log(`\u2705 Done. Results saved to ${OUTPUT_PATH}`);
})();
