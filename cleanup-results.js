const fs = require('fs');

// Function to determine if a URL is essential for redirect analysis
function isEssentialUrl(url) {
  // Keep API endpoints, tracking URLs, and actual app store links
  const essentialPatterns = [
    /^https:\/\/api\.mychips\.io/,
    /^https:\/\/trk\d+\.com/,
    /^https:\/\/play\.google\.com\/store\/apps\/details/,
    /^https:\/\/apps\.apple\.com/,
    /^https:\/\/kloverapp\.go2cloud\.org/,
    /redirect/,
    /tracking/,
    /affiliate/,
    /aff_c/
  ];

  // Skip static resources
  const staticPatterns = [
    /\.(js|css|woff2?|png|jpg|jpeg|gif|svg|ico|mp4|webp)(\?|$)/,
    /fonts\.gstatic\.com/,
    /ssl\.gstatic\.com/,
    /play-lh\.googleusercontent\.com/,
    /i\.ytimg\.com/,
    /www\.gstatic\.com.*\/js\//,
    /google-analytics\.com/,
    /googletagmanager\.com/,
    /feedback\/js/,
    /gstatic\.com.*\/boq-play/
  ];

  // Skip if it's a static resource
  for (const pattern of staticPatterns) {
    if (pattern.test(url)) {
      return false;
    }
  }

  // Keep if it matches essential patterns
  for (const pattern of essentialPatterns) {
    if (pattern.test(url)) {
      return true;
    }
  }

  // Keep main domain redirects but skip deep resource paths
  if (url.includes('play.google.com') && !url.includes('/js/') && !url.includes('/css/') && !url.includes('/_/')) {
    return true;
  }

  return false;
}

// Function to clean redirect chain
function cleanRedirectChain(redirectChain) {
  if (!redirectChain || redirectChain.length === 0) {
    return [];
  }

  return redirectChain
    .filter(entry => isEssentialUrl(entry.url))
    .map(entry => ({
      url: entry.url,
      // Only include cookies if they're not empty and from essential domains
      ...(entry.cookies && entry.cookies.length > 0 &&
          (entry.url.includes('google.com') || entry.url.includes('mychips.io'))
          ? { cookies: entry.cookies.slice(0, 1).map(cookie => ({
              name: cookie.name,
              domain: cookie.domain
            })) } // Simplified cookie info
          : {})
    }));
}

// Function to simplify user agent
function simplifyUserAgent(userAgent) {
  if (!userAgent) return '';

  // Extract key info: platform, device, browser
  const match = userAgent.match(/(Mozilla\/[\d.]+)\s*\(([^)]+)\).*?(Chrome\/[\d.]+|Safari\/[\d.]+|Firefox\/[\d.]+)/);
  if (match) {
    const platform = match[2].split(';')[0].trim();
    const browser = match[3];
    return `${platform} - ${browser}`;
  }

  return userAgent.substring(0, 50) + '...';
}

// Function to clean mychips results
function cleanMychipsResults() {
  try {
    const data = JSON.parse(fs.readFileSync('mychipsresults.json', 'utf8'));

    // Remove duplicates based on id and title
    const seen = new Set();
    const uniqueResults = data.filter(item => {
      const key = `${item.id}-${item.title}-${item.status}`;
      if (seen.has(key)) {
        return false;
      }
      seen.add(key);
      return true;
    });

    // Clean each result
    const cleanedResults = uniqueResults.map(result => ({
      id: result.id,
      title: result.title,
      attemptedWith: result.attemptedWith,
      status: result.status,
      redirectChain: cleanRedirectChain(result.redirectChain),
      ...(result.error ? { error: result.error } : {})
    }));

    fs.writeFileSync('mychipsresults_cleaned.json', JSON.stringify(cleanedResults, null, 2));
    console.log(`✅ Cleaned mychips results: ${data.length} -> ${cleanedResults.length} entries`);

    // Show redirect chain size reduction
    const originalRedirects = data.reduce((sum, item) => sum + (item.redirectChain?.length || 0), 0);
    const cleanedRedirects = cleanedResults.reduce((sum, item) => sum + (item.redirectChain?.length || 0), 0);
    console.log(`   Redirect chains reduced: ${originalRedirects} -> ${cleanedRedirects} URLs`);

  } catch (error) {
    console.error('❌ Error cleaning mychips results:', error.message);
  }
}

// Function to clean redirect results
function cleanRedirectResults() {
  try {
    const data = JSON.parse(fs.readFileSync('redirect_results.json', 'utf8'));

    // Group by similar patterns to identify repetitive data
    const uniqueResults = [];
    const seenPatterns = new Map();

    for (const result of data) {
      // Create a pattern key based on the redirect URL structure
      const firstUrl = result.redirectChain?.[0]?.url || '';
      const pattern = firstUrl.replace(/offer_id=\d+/, 'offer_id=X')
                              .replace(/aff_unique1=[a-f0-9]+/, 'aff_unique1=X');

      if (!seenPatterns.has(pattern)) {
        seenPatterns.set(pattern, []);
      }
      seenPatterns.get(pattern).push(result);
    }

    // Keep only a sample from each pattern group
    for (const [pattern, results] of seenPatterns) {
      // Keep first few examples of each pattern
      const samplesToKeep = Math.min(3, results.length);
      for (let i = 0; i < samplesToKeep; i++) {
        const result = results[i];
        uniqueResults.push({
          offerId: result.offerId,
          userAgent: simplifyUserAgent(result.userAgent),
          ip: result.ip,
          redirectChain: cleanRedirectChain(result.redirectChain),
          status: result.status
        });
      }
    }

    fs.writeFileSync('redirect_results_cleaned.json', JSON.stringify(uniqueResults, null, 2));
    console.log(`✅ Cleaned redirect results: ${data.length} -> ${uniqueResults.length} entries`);
    console.log(`   Found ${seenPatterns.size} unique redirect patterns`);

  } catch (error) {
    console.error('❌ Error cleaning redirect results:', error.message);
  }
}

// Run cleanup
console.log('🧹 Starting cleanup process...\n');
cleanMychipsResults();
cleanRedirectResults();
console.log('\n✨ Cleanup completed!');
console.log('\nFiles created:');
console.log('- mychipsresults_cleaned.json');
console.log('- redirect_results_cleaned.json');
