const fs = require('fs');

function extractPostbacks() {
  try {
    console.log('📖 Reading redirect_results_filtered.json...');
    const data = JSON.parse(fs.readFileSync('redirect_results_filtered.json', 'utf8'));
    
    console.log(`📊 Analyzing ${data.length} offers for postbacks...`);
    
    const postbackData = [];
    
    data.forEach(offer => {
      const offerId = offer.offerId;
      const redirectChain = offer.redirectChain || [];
      
      // Look for postback URLs in the redirect chain
      const postbacks = redirectChain.filter(redirect => {
        const url = redirect.url.toLowerCase();
        return url.includes('postback') || 
               url.includes('callback') || 
               url.includes('pixel') ||
               url.includes('conversion') ||
               url.includes('track') ||
               url.includes('event') ||
               url.includes('complete') ||
               url.includes('success');
      });
      
      // Also look for URLs with common postback parameters
      const parameterBasedPostbacks = redirectChain.filter(redirect => {
        const url = redirect.url;
        return url.includes('click_id=') ||
               url.includes('transaction_id=') ||
               url.includes('conversion_id=') ||
               url.includes('sub_id=') ||
               url.includes('aff_sub=') ||
               url.includes('s1=') ||
               url.includes('s2=') ||
               url.includes('payout=') ||
               url.includes('revenue=');
      });
      
      // Combine both types
      const allPostbacks = [...new Set([...postbacks, ...parameterBasedPostbacks])];
      
      if (allPostbacks.length > 0) {
        // Extract relevant cookies that might be needed
        const relevantCookies = [];
        redirectChain.forEach(redirect => {
          if (redirect.cookies && redirect.cookies.length > 0) {
            redirect.cookies.forEach(cookie => {
              if (cookie.name.includes('click') ||
                  cookie.name.includes('track') ||
                  cookie.name.includes('session') ||
                  cookie.name.includes('id') ||
                  cookie.name.includes('aff') ||
                  cookie.name.includes('sub')) {
                relevantCookies.push({
                  name: cookie.name,
                  domain: cookie.domain,
                  value: cookie.value ? cookie.value.substring(0, 50) + '...' : 'N/A'
                });
              }
            });
          }
        });
        
        // Extract URL parameters that might be needed
        const relevantParams = new Set();
        allPostbacks.forEach(postback => {
          const url = new URL(postback.url);
          url.searchParams.forEach((value, key) => {
            if (key.includes('click') ||
                key.includes('track') ||
                key.includes('id') ||
                key.includes('sub') ||
                key.includes('aff') ||
                key.includes('conversion') ||
                key.includes('transaction') ||
                key.includes('payout') ||
                key.includes('revenue')) {
              relevantParams.add(key);
            }
          });
        });
        
        postbackData.push({
          offerId: offerId,
          postbackUrls: allPostbacks.map(pb => pb.url),
          relevantCookies: relevantCookies,
          relevantParameters: Array.from(relevantParams),
          totalRedirects: redirectChain.length
        });
      }
    });
    
    // Sort by offer ID
    postbackData.sort((a, b) => a.offerId - b.offerId);
    
    // Save results
    fs.writeFileSync('postback_analysis.json', JSON.stringify(postbackData, null, 2));
    
    console.log(`✅ Found postbacks in ${postbackData.length} offers`);
    console.log(`💾 Saved analysis to: postback_analysis.json`);
    
    // Show summary
    console.log('\n📋 Summary:');
    postbackData.slice(0, 5).forEach(offer => {
      console.log(`\n🎯 Offer ${offer.offerId}:`);
      console.log(`   Postbacks: ${offer.postbackUrls.length}`);
      offer.postbackUrls.forEach((url, index) => {
        const domain = new URL(url).hostname;
        console.log(`   ${index + 1}. ${domain}`);
      });
      if (offer.relevantParameters.length > 0) {
        console.log(`   Key params: ${offer.relevantParameters.join(', ')}`);
      }
      if (offer.relevantCookies.length > 0) {
        console.log(`   Key cookies: ${offer.relevantCookies.map(c => c.name).join(', ')}`);
      }
    });
    
    if (postbackData.length > 5) {
      console.log(`\n... and ${postbackData.length - 5} more offers`);
    }
    
  } catch (error) {
    console.error('❌ Error extracting postbacks:', error.message);
  }
}

// Run the extraction
console.log('🔍 Starting postback extraction...\n');
extractPostbacks();
console.log('\n✨ Extraction completed!');
